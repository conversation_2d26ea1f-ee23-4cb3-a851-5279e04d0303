import * as Resgate from '@src/features/financeiro/resgate/exports';

export const SolicitacaoResgate = (): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const theme = Resgate.useTheme();

  const {
    handleChangeCamposNovaContaResgate,
    temContaPreenchida,
    selecionarContaResgate,
  } = Resgate.useFormularioContaResgate();

  const {
    obterAssinatura,
    confirmarAssinaturaResgate,
    isLoadingConfirmarAssinatura,
    podeExibirAssinatura,
    assinaturaValida,
  } = Resgate.useAssinaturaResgate(temContaPreenchida);

  const {
    mensagens,
    isDisabledProsseguirResgate,
    isLoadingSolicitarResgate,
    efetuarSolicitacaoResgate,
  } = Resgate.useEfetuarSolicitacaoResgate({
    temContaPreenchida,
    confirmarAssinaturaResgate,
  });

  const { fundosContribuicaoRegular, deveRenderizarTabelaContribuicao } =
    Resgate.useContribuicaoRegular(temContaPreenchida);

  return (
    <Resgate.Grid margin="18" container>
      <Resgate.GridItem xs="1">
        <Resgate.ContainerTabelaResumoResgate>
          <Resgate.Text variant="text-standard-600">
            {Resgate.obterDescricaoAliquota(
              resgateFeatureData.resumoAliquotaSelecionada?.tipoAliquota,
            )}
            <Resgate.TooltipInfoAliquota
              tipoAliquota={
                resgateFeatureData.resumoAliquotaSelecionada?.tipoAliquota
              }
            />
          </Resgate.Text>
          <Resgate.Text variant="text-standard-400">
            Dados do resgate
          </Resgate.Text>
          <Resgate.TabelaResumoSimulacao />
        </Resgate.ContainerTabelaResumoResgate>
      </Resgate.GridItem>

      <Resgate.MotivoResgate />

      <Resgate.FormularioContaResgate
        selecionarContaResgate={selecionarContaResgate}
        handleChangeCamposNovaContaResgate={handleChangeCamposNovaContaResgate}
      />

      <Resgate.ContribuicaoRegular
        fundosContribuicaoRegular={fundosContribuicaoRegular}
        deveRenderizarTabelaContribuicao={deveRenderizarTabelaContribuicao}
      />

      <Resgate.AssinaturaResgate
        obterAssinatura={obterAssinatura}
        podeExibirAssinatura={podeExibirAssinatura}
      />

      <Resgate.ErroSolicitacaoResgate mensagens={mensagens} />

      <Resgate.GridItem xs="1">
        <Resgate.ContainerButtons>
          <Resgate.Button
            size="standard"
            variant="secondary"
            onClick={efetuarSolicitacaoResgate}
            disabled={Resgate.checkIfSomeItemsAreTrue([
              isDisabledProsseguirResgate,
              !assinaturaValida,
              isLoadingConfirmarAssinatura,
            ])}
            leftIcon={
              <Resgate.ConditionalRenderer
                condition={Resgate.checkIfSomeItemsAreTrue([
                  isLoadingSolicitarResgate,
                  isLoadingConfirmarAssinatura,
                ])}
              >
                <Resgate.LoadingSpinner
                  size="small"
                  color={theme.color.content.neutral['01']}
                />
              </Resgate.ConditionalRenderer>
            }
          >
            Prosseguir
          </Resgate.Button>
        </Resgate.ContainerButtons>
      </Resgate.GridItem>
    </Resgate.Grid>
  );
};
