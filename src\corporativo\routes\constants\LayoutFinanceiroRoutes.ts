import { TRoute } from '../types/RoutesTypes';

const LayoutFinanceiroRoutes: TRoute[] = [
  {
    name: 'Dados do Pagamento',
    path: 'dados-do-pagamento',
  },
  {
    name: 'Transferências',
    path: 'transferencias',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    path: 'aporte',
  },
  {
    name: 'Resgate',
    path: 'resgate',
  },

  {
    name: 'Ativação / Suspensão de contribuição',
    path: 'ativacao-suspensao-contribuicao',
  },
  {
    name: 'Alteração de regime tributário',
    path: 'regime-tributario',
  },
];
export default LayoutFinanceiroRoutes;
