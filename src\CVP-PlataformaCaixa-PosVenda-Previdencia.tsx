import React from 'react';
import ReactDOMClient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import App from './App';
import { enableHMR } from './utils/hmr-helper';
import './utils/hmr-setup'; // Configuração global do HMR
import './utils/runtime-error-recovery';

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  errorBoundary(err, info) {
    return (
      <>
        <h1>Oop! 500</h1>
        <pre>{err.stack}</pre>
        <pre>{info.componentStack}</pre>
      </>
    );
  },
});

export const { bootstrap, mount, unmount } = lifecycles;

// Habilita HMR para desenvolvimento
enableHMR();
