import { Table, Button } from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const AtivacaoSuspensaoTable = styled(Table)`
  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #edf4f6;
      border: none;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;

      .rdt_TableCol_Sortable {
        font-weight: 600;
      }
    }
  }

  .rdt_TableBody {
    .rdt_TableRow:nth-child(2n) {
      background-color: #edf4f6;
    }
  }
`;

export const StatusButton = styled(Button)<{ $ativo: boolean }>`
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: ${({ $ativo }) => ($ativo ? '#28a745' : '#dc3545')};
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    opacity: 0.8;
  }
`;

export const ContainerPrincipal = styled.div`
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 1px rgba(0, 0, 0, 0.15);
`;

export const TituloSecao = styled.div`
  margin-bottom: 24px;
`;

export const ContribuicaoCard = styled.div``;

export const TotalContainer = styled.div`
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
`;

export const TituloTabelaConfirmacao = styled.div`
  background-color: #f1f5f9;
  padding: 12px 20px;
  margin-bottom: 0;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #374151;
  font-size: 16px;
`;

export const LinkEstilizado = styled.span`
  text-decoration: underline;
  cursor: pointer;
  color: inherit;
`;

export const TabelaConfirmacao = styled(Table)`
  border: none;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  margin-top: 0;

  .rdt_TableHead {
    display: none;
  }

  .rdt_Table {
    background-color: transparent;
    margin: 0;
  }

  .rdt_TableBody {
    background-color: transparent;

    .rdt_TableRow {
      border: none;
      border-bottom: none;
      border-top: none;
      min-height: 48px;

      &:nth-child(odd) {
        background-color: #f8f9fa;
        background: #f8f9fa;
      }

      &:nth-child(even) {
        background-color: #ffffff;
        background: #ffffff;
      }

      &:hover {
        background-color: inherit;
      }

      .rdt_TableCell {
        border: none;
        border-bottom: none;
        border-top: none;
        padding: 12px 20px;
        vertical-align: middle;

        &:first-child {
          font-weight: 400;
          color: #6b7280;
          text-align: left;
          width: 60%;
        }

        &:last-child {
          font-weight: 600;
          color: #374151;
          text-align: right;
          width: 40%;
        }
      }
    }
  }
`;
