{"name": "mfe-seguridade-previdencia-pos", "scripts": {"start": "cross-env BABEL_ENV=development webpack serve --port 4002 --env development", "start:hm": "webpack serve --port 4002 --env homologation", "start:standalone": "webpack serve --port 4002 --env standalone", "build": "concurrently npm:build:*", "build:dev": "webpack --mode=development --env development", "postbuild:dev": "node tools/copyIISConfig.js", "build:hom": "webpack --mode=production --env homologation", "postbuild:hom": "node tools/copyIISConfig.js", "build:pre-prd": "webpack --mode=production --env pre-production", "postbuild:pre-prd": "node tools/copyIISConfig.js", "build:prd": "webpack --mode=production --env production", "postbuild:prd": "node tools/copyIISConfig.js", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "jest", "watch-tests": "jest --watch", "coverage": "jest --coverage", "build:types": "tsc", "commit": "npm run lint && npm run test && cz"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/eslint-parser": "^7.24.8", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.8", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.24.8", "@babel/runtime-corejs3": "^7.24.8", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@types/eslint": "^8.56.10", "@types/faker": "^6.6.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "babel-jest": "^29.7.0", "babel-plugin-dynamic-import-node": "^2.3.3", "commitizen": "^4.3.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-custom-rules": "^0.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-rules": "^0.1.9", "file-loader": "^6.2.0", "husky": "^9.1.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "ts-jest": "^29.2.3", "ts-node": "^10.9.2", "typescript": "^5.5.3", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-config-single-spa-react": "^4.0.5", "webpack-config-single-spa-react-ts": "^4.0.5", "webpack-config-single-spa-ts": "^4.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^6.0.1"}, "dependencies": {"@cvp/componentes-posvenda": "1.9.3", "@cvp/design-system-caixa": "2.0.1", "@cvp/logging-extensions": "^2.0.1", "@cvp/utils": "^1.0.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^8.4.1", "@mui/icons-material": "^6.1.6", "@mui/material": "^6.1.6", "@types/jest": "^29.5.12", "@types/systemjs": "^6.13.5", "@types/webpack-env": "^1.18.5", "axios": "^1.7.2", "dotenv": "^16.4.5", "faker": "^6.6.6", "formik": "^2.4.6", "jest-environment-jsdom": "^29.7.0", "material-icons": "^1.13.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.25.1", "single-spa": "^6.0.1", "single-spa-react": "^6.0.1", "styled-component": "^2.8.0", "styled-components": "6.1.16", "yup": "^1.6.1"}, "types": "dist/cvp-mfe-cvp.d.ts", "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["build", "chore", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test"]]}}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && npx cz --hook || true", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS || echo 'Use npm run commit para Commits Semânticos'"}}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}}