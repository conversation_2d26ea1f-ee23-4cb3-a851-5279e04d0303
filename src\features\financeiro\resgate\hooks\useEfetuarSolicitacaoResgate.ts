import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useEfetuarSolicitacaoResgate = ({
  temContaPreenchida,
  confirmarAssinaturaResgate,
}: Resgate.IUseEfetuarSolicitacaoResgate): Resgate.IUseEfetuarSolicitacaoResgateRetorno => {
  const { resgateFeatureData, handleResgateFeatureData, handleEtapa } =
    Resgate.useResgateContext();

  const numeroResgateConsolidado: string = Resgate.tryGetValueOrDefault(
    [resgateFeatureData?.numeroResgateConsolidado],
    '',
  );

  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const { mensagens, configurarMensagem } = Resgate.useMensagensTemporarias();

  const { isLoadingValidacaoConta, validarConta } = Resgate.useValidarConta();

  const { isLoadingDadosPagamento, salvarDadosPagamento } =
    Resgate.useSalvarDadosPagamento();

  const { isLoadingCriacaoMotivoResgate, criarMotivoResgate } =
    Resgate.useCriarMotivoResgate();

  const { isLoadingDefinicaoContribuicaoRegular, definirContribuicaoRegular } =
    Resgate.useDefinirContribuicaoRegular();

  const { isLoadingConfirmacaoResgate, confirmarResgate } =
    Resgate.useConfirmarResgate();

  const isLoadingSolicitarResgate: boolean = Resgate.checkIfSomeItemsAreTrue([
    isLoadingValidacaoConta,
    isLoadingDadosPagamento,
    isLoadingCriacaoMotivoResgate,
    isLoadingConfirmacaoResgate,
    isLoadingDefinicaoContribuicaoRegular,
  ]);

  const isDisabledProsseguirResgate: boolean = Resgate.checkIfSomeItemsAreTrue([
    isLoadingSolicitarResgate,
    !formik.values.motivoResgate,
    !temContaPreenchida,
  ]);

  const isNovaContaCaixa: boolean = Resgate.checkIfAllItemsAreTrue([
    formik.values.novaConta.banco.value === Resgate.CODIGO_BANCO_CAIXA,
    formik.values.isNovaConta,
  ]);

  const payloadContaCaixa = {
    codigoAgencia: formik.values.novaConta.agencia,
    codigoOperacao: formik.values.novaConta.operacao,
    digitoVerificador: formik.values.novaConta.digito,
    numeroBanco: Resgate.CODIGO_BANCO_CAIXA,
    numeroConta: formik.values.novaConta.conta,
  };

  const payloadPagamento: Resgate.TCriarPayloadDadosPagamentoFactoryReturn =
    Resgate.criarPayloadDadosPagamentoFactory(
      numeroResgateConsolidado,
      formik.values,
    );

  const payloadMotivoResgate = {
    codigoDoMotivo: formik.values.motivoResgate,
    numeroDoResgate: numeroResgateConsolidado,
  };

  const payloadContribuicaoRegular: Resgate.ICriarPayloadContribuicaoRegularFactoryReturn =
    Resgate.criarPayloadContribuicaoRegularFactory(
      resgateFeatureData?.contribuicaoRegular?.fundos,
      numeroResgateConsolidado,
    );

  const validarNovaContaCaixa = async (): Promise<boolean> => {
    if (isNovaContaCaixa) {
      const dadosValidacaoConta = await validarConta(payloadContaCaixa);

      const codigoInvalidoCaixa: boolean =
        dadosValidacaoConta?.entidade?.codigoRetorno !==
        Resgate.DADOS_BANCO_CONTA.CODIGO_VALIDO_CAIXA;

      if (codigoInvalidoCaixa) {
        return Resgate.retornarMensagemFluxoSolicitacao({
          dadosServico: dadosValidacaoConta,
          configurarMensagem,
          erroCustom:
            Resgate.MSG_TEMPORARIA.SOLICITACAO_RESGATE.VALIDA_CONTA_CAIXA,
        });
      }
    }

    return true;
  };

  const obterConfirmacaoDadosPagamento = async (): Promise<boolean> => {
    const dadosPagamento = await salvarDadosPagamento(payloadPagamento);

    return Resgate.retornarMensagemFluxoSolicitacao({
      dadosServico: dadosPagamento,
      configurarMensagem,
      erroCustom:
        Resgate.MSG_TEMPORARIA.SOLICITACAO_RESGATE.CONFIRMA_DADOS_PAGAMENTO,
    });
  };

  const obterConfirmacaoCriacaoMotivoResgate = async (): Promise<boolean> => {
    const dadosCriarMotivoResgate = await criarMotivoResgate(
      payloadMotivoResgate,
    );

    return Resgate.retornarMensagemFluxoSolicitacao({
      dadosServico: dadosCriarMotivoResgate,
      configurarMensagem,
      erroCustom:
        Resgate.MSG_TEMPORARIA.SOLICITACAO_RESGATE.CRIA_MOTIVO_RESGATE,
    });
  };

  const obterDefinicaoContribuicaoRegular = async (): Promise<boolean> => {
    if (resgateFeatureData?.contribuicaoRegular?.permiteDefinirContribRegular) {
      const dadosDefinicaoContribuicaoRegular =
        await definirContribuicaoRegular(payloadContribuicaoRegular);

      return Resgate.retornarMensagemFluxoSolicitacao({
        dadosServico: dadosDefinicaoContribuicaoRegular,
        configurarMensagem,
        erroCustom:
          Resgate.MSG_TEMPORARIA.SOLICITACAO_RESGATE
            .DEFINE_CONTRIBUICAO_REGULAR,
      });
    }

    return true;
  };

  const obterConfirmacaoResgate = async (): Promise<boolean> => {
    const dadosConfirmacaoResgate = await confirmarResgate({
      numeroResgate: numeroResgateConsolidado,
    });

    const confirmacaoAssinatura = await confirmarAssinaturaResgate(
      numeroResgateConsolidado,
    );

    const isSucessoConfirmacaoResgate: boolean =
      Resgate.retornarMensagemFluxoSolicitacao({
        dadosServico: dadosConfirmacaoResgate,
        configurarMensagem,
        erroCustom: Resgate.MSG_TEMPORARIA.SOLICITACAO_RESGATE.CONFIRMA_RESGATE,
      });

    const isSucessoFluxo: boolean = Resgate.checkIfAllItemsAreTrue([
      isSucessoConfirmacaoResgate,
      confirmacaoAssinatura,
    ]);

    handleResgateFeatureData({
      dadosRetornoConfirmacaoResgate: {
        statusResgate: Resgate.tryGetValueOrDefault(
          [dadosConfirmacaoResgate?.entidade?.status],
          '',
        ),
        motivoPendenciaResgate: Resgate.tryGetValueOrDefault(
          [dadosConfirmacaoResgate?.entidade?.mensagem],
          '',
        ),
      },
    });

    if (isSucessoFluxo) {
      handleEtapa(Resgate.EEtapasResgate.SUCESSO);
    }

    return isSucessoFluxo;
  };

  const efetuarSolicitacaoResgate = async (): Promise<void> => {
    const isFalhaValidacaoConta = !(await validarNovaContaCaixa());

    if (isFalhaValidacaoConta) return;

    const isFalhaValidacaoCriacaoMotivoResgate =
      !(await obterConfirmacaoCriacaoMotivoResgate());

    if (isFalhaValidacaoCriacaoMotivoResgate) return;

    const isFalhaValidacaoConfirmacaoPagamento =
      !(await obterConfirmacaoDadosPagamento());

    if (isFalhaValidacaoConfirmacaoPagamento) return;

    const isFalhaValidacaoDefinicaoContribuicao =
      !(await obterDefinicaoContribuicaoRegular());

    if (isFalhaValidacaoDefinicaoContribuicao) return;

    await obterConfirmacaoResgate();
  };

  return {
    mensagens,
    isDisabledProsseguirResgate,
    isLoadingSolicitarResgate,
    efetuarSolicitacaoResgate,
  };
};
