export {
  For,
  LayoutPlataforma,
  Match,
  SwitchCase,
  useFilter,
} from '@cvp/componentes-posvenda';
export {
  Grid,
  GridItem,
  IconPVPlusCircle,
  Separator,
  Text,
  useTheme,
  IconCancelOutlinedSharp,
  Button,
  MultiAccordion,
  TabNav,
  LoadingSpinner,
} from '@cvp/design-system-caixa';
export { CertificadoContentLoading } from '@src/features/layoutPrevidencia/views/CertificadoContentLoading';
export type { IListaCertificadoReturn } from '@src/features/layoutPrevidencia/types/IListaCertificadosReturn';
export { default as PREVIDENCIA_CONSTANTS } from '@src/corporativo/constants/PrevidenciaConstants';
export { ModalImprimirDocumentosContext } from '@src/corporativo/context/ModalImprimirDocumentosContext';
export { default as useCertificadosPrevidencia } from '@src/corporativo/infra/consultaCertificado/useCertificadosPrevidencia';
export { default as CertificadoInfo } from '@src/features/layoutPrevidencia/components/CertificadoInfo';
export { default as tabsFromRouterFactory } from '@src/features/layoutPrevidencia/factory/TabsFromRouterFactory';
export { default as useTabsFromRouter } from '@src/features/layoutPrevidencia/hook/useTabsFromRouter';
export { default as ContainerListaCertificados } from '@src/features/layoutPrevidencia/views/ContainerListaCertificados';
export { default as LayoutConsulta } from '@src/features/layoutPrevidencia/views/LayoutPrevidencia';
export { default as ListaCertificados } from '@src/features/layoutPrevidencia/views/ListaCertificados';
export { default as StatusPrevidenciaMapper } from '@src/features/layoutPrevidencia/views/StatusPrevidenciaMapper';
export { REDIRECT_BUTTONS } from '@src/shared/factory/botoesRedirect';
export { Outlet, useLocation, useNavigate } from 'react-router-dom';
export {
  default as React,
  useContext,
  useEffect,
  useLayoutEffect,
} from 'react';

export { default as ApoliceIcon } from '@src/corporativo/components/ApoliceIcon';
export { default as CalendarioIcon } from '@src/corporativo/components/CalendarioIcon';
export { default as DocumentIcon } from '@src/corporativo/components/DocumentIcon';
export { default as PDFIcon } from '@src/corporativo/components/PDFIcon';
export { default as BotoesApolice } from '@src/features/layoutPrevidencia/components/BotoesApolice';
export { DescricaoStatusPrevidencia } from '@src/features/layoutPrevidencia/constants/DescricaoStatusPrevidencia';
export {
  Badge,
  CertificadoPrevidenciaWrapper,
} from '@src/features/layoutPrevidencia/styles/style';

export { MatrizAcessoContext } from '@src/corporativo/context/MatrizAcessoContext';
export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { default as useObterBeneficiarios } from '@src/corporativo/infra/beneficiarios/useObterBeneficiarios';
export type { IDadosTabelaProdutosPrevidencia } from '@src/corporativo/types/consultaCertificado/IDadosTabelaProdutosPrevidencia';
export { default as FiltroPropostaFactroy } from '@src/features/layoutPrevidencia/factory/FiltroPropostaFactroy';
export { default as useListaCertificados } from '@src/features/layoutPrevidencia/hook/useListaCertificados';
export type { ICertificadoPrevidenciaResponse } from '@src/shared/types/ICertificadoPrevidenciaResponse';
export type {
  TProfileFilters,
  TStatusContratoFilter,
} from '@src/shared/types/TProfileFilters';

export * as Utils from '@cvp/utils';
export * from '@src/features/layoutPrevidencia/constants/imprDocModalTextos';
export * from '@src/features/layoutPrevidencia/factory/modalImprDocFactory';
export { default as useModalImprDoc } from '@src/features/layoutPrevidencia/hook/useModalImprDoc';
export { default as ImprimirDocumento } from '@src/features/layoutPrevidencia/pages/ImprimirDocumento';
export * from '@src/features/layoutPrevidencia/types/modalImprDocTypes';
export * from '@src/features/layoutPrevidencia/utils';
export { default as ModalImprDoc } from '@src/features/layoutPrevidencia/views/ModalImprDoc';
export { LOADING, LOADING_INSURANCE } from '@src/shared/constants/api';

export type { IButtonRedirectProps } from '@cvp/componentes-posvenda/types/types/components/ILayoutPlataforma';

export { useObterCoberturas } from '@src/corporativo/infra/obterCoberturas/useObterCoberturas';

export type {
  ICertificadoContentFactory,
  ICertificadoContentFactoryReturn,
} from '@src/features/layoutPrevidencia/types/ICertificadoContentFactory';

export { LABEL_DESCRIPTION } from '@src/features/layoutPrevidencia/constants/LabelDescription';

export { CertificadoContentFactory } from '@src/features/layoutPrevidencia/factory/CertificadoContentFactory';
export { GridItemPersonalizado } from '@src/features/layoutPrevidencia/styles/style';

export { default as CertificadoContent } from '@src/features/layoutPrevidencia/components/CertificadoContent';

export type { TUseObterCoberturasResponseReturn } from '@src/corporativo/types/obterCoberturas/TUseObterCoberturasResponse';

export type { ITheme } from '@cvp/design-system-caixa';

export { EModalImprDocActionType } from '@src/features/layoutPrevidencia/types/modalImprDocTypes';

export {
  retornarTodasPermissoesDaCategoria,
  verificarSeMatrizAcessoPossuiAlgumaPermissao,
  verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias,
} from '@src/corporativo/utils/matrizAcesso';

export {
  BOTOES_APOLICES_PERMISSOES,
  IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES,
} from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';
export { useRegistrarTokenOperador } from '@src/corporativo/hooks/useRegistrarTokenOperador';
