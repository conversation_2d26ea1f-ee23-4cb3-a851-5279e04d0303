import {
  FiltroPropostaFactroy,
  IListaCertificadoReturn,
  PrevidenciaContext,
  useCertificadosPrevidencia,
  useContext,
  useEffect,
  useFilter,
} from '@src/features/layoutPrevidencia/exports';

const useListaCertificados = (): IListaCertificadoReturn => {
  const { setStatusContratoFilter, setCertificadoAtivo } =
    useContext(PrevidenciaContext);
  const { response, loading } = useCertificadosPrevidencia();

  const { filterResponse } = useFilter();
  const filteredResponse = filterResponse(response);

  useEffect(() => {
    const status = FiltroPropostaFactroy(response);
    setStatusContratoFilter(status);
  }, [response, setStatusContratoFilter]);

  return { filteredResponse, setCertificadoAtivo, loading };
};

export default useListaCertificados;
