import React from 'react';
import * as AtivacaoSuspensao from '../../exports';
import { IContribuicaoParaSuspender } from '../../hooks/useSolicitacaoSuspensao';
import {
  AlertasSuspensao,
  LoadingSection,
  ConteudoPrincipal,
  Estado<PERSON>sso,
  TituloSecao,
} from './components';
import { useSolicitacaoContentState } from './hooks/useSolicitacaoContentState';

interface ISolicitacaoAtivacaoSuspensaoContentProps {
  loadingCertificado: boolean;
  dadosCertificado: AtivacaoSuspensao.IRecuperarContribuicoesCertificadoResponse | null;
  showConfirmacao: boolean;
  contribuicaoParaSuspender: IContribuicaoParaSuspender | null;
  confirmacaoFinalizada: boolean;
  clienteAceita: boolean;
  loadingSuspender: boolean;
  erroSuspensao: string | null;
  onClienteAceitaChange: (value: boolean) => void;
  onConfirmarSuspensao: (
    toggleContribuicao: (index: number) => void,
  ) => () => Promise<void>;
  onVoltarConfirmacao: () => void | Promise<void>;
  onToggleContribuicao: (
    contribuicoes: AtivacaoSuspensao.IContribuicaoItem[],
    toggleContribuicao: (index: number) => void,
  ) => (index: number, novoStatus: boolean) => void;
  onGerarComprovante: () => void;
}

export const SolicitacaoAtivacaoSuspensaoContent: React.FC<
  ISolicitacaoAtivacaoSuspensaoContentProps
> = ({
  loadingCertificado,
  dadosCertificado,
  showConfirmacao,
  contribuicaoParaSuspender,
  confirmacaoFinalizada,
  clienteAceita,
  loadingSuspender,
  erroSuspensao,
  onClienteAceitaChange,
  onConfirmarSuspensao,
  onVoltarConfirmacao,
  onToggleContribuicao,
  onGerarComprovante,
}) => {
  const { contribuicoes, loading, toggleContribuicao } =
    AtivacaoSuspensao.useAtivacaoSuspensaoContribuicaoContext();

  const { isLoading, shouldShowMainTable, shouldShowSuccessState } =
    useSolicitacaoContentState({
      loading,
      loadingCertificado,
      loadingSuspender,
      showConfirmacao,
      erroSuspensao,
      confirmacaoFinalizada,
    });

  return (
    <AtivacaoSuspensao.ContainerPrincipal>
      <AtivacaoSuspensao.Grid>
        <TituloSecao />

        <AlertasSuspensao
          shouldShowSuccessState={shouldShowSuccessState}
          erroSuspensao={erroSuspensao}
          contribuicaoParaSuspender={contribuicaoParaSuspender}
        />

        <AtivacaoSuspensao.GridItem xs="1">
          <LoadingSection
            isLoading={isLoading}
            loadingSuspender={loadingSuspender}
            contribuicaoParaSuspender={contribuicaoParaSuspender}
          />

          <ConteudoPrincipal
            shouldShowMainTable={shouldShowMainTable}
            showConfirmacao={showConfirmacao}
            loadingSuspender={loadingSuspender}
            contribuicoes={contribuicoes}
            contribuicaoParaSuspender={contribuicaoParaSuspender}
            clienteAceita={clienteAceita}
            dadosCertificado={dadosCertificado}
            onToggleContribuicao={onToggleContribuicao}
            onClienteAceitaChange={onClienteAceitaChange}
            onConfirmarSuspensao={onConfirmarSuspensao}
            onVoltarConfirmacao={onVoltarConfirmacao}
            toggleContribuicao={toggleContribuicao}
          />
        </AtivacaoSuspensao.GridItem>

        <EstadoSucesso
          shouldShowSuccessState={shouldShowSuccessState}
          contribuicoes={contribuicoes}
          onToggleContribuicao={onToggleContribuicao}
          onGerarComprovante={onGerarComprovante}
          toggleContribuicao={toggleContribuicao}
        />
      </AtivacaoSuspensao.Grid>
    </AtivacaoSuspensao.ContainerPrincipal>
  );
};
