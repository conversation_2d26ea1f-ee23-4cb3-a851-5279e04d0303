import { useEffect, useCallback } from 'react';
import * as AtivacaoSuspensao from '../exports';
import { IContribuicaoParaSuspender } from './useSolicitacaoSuspensao';

interface IUseLogicaSuspensaoProps {
  contribuicaoParaSuspender: IContribuicaoParaSuspender | null;
  clienteAceita: boolean;
  onSucessoSuspensao: () => void;
  onErroSuspensao: (mensagem: string) => void;
  onRecarregarDados: () => void;
}

interface IUseLogicaSuspensaoReturn {
  loadingSuspender: boolean;
  confirmarSuspensao: (
    toggleContribuicao: (index: number) => void,
  ) => Promise<void>;
  limparRespostas: () => void;
}

export const useLogicaSuspensao = ({
  contribuicaoParaSuspender,
  clienteAceita,
  onSucessoSuspensao,
  onErroSuspensao,
  onRecarregarDados,
}: IUseLogicaSuspensaoProps): IUseLogicaSuspensaoReturn => {
  // Hook para suspender cuidado extra
  const {
    loading: loadingSuspenderCuidado,
    response: responseSuspenderCuidado,
    fetchData: suspenderCuidadoExtra,
    setResponse: setResponseSuspenderCuidado,
  } = AtivacaoSuspensao.useSuspenderCuidadoExtra(
    contribuicaoParaSuspender?.tipo === 'cuidadoExtra'
      ? {
          NumeroBeneficio: contribuicaoParaSuspender.beneficioId,
          NumeroPlano: contribuicaoParaSuspender.planoId,
          TipoContribuicao: contribuicaoParaSuspender.tipoContribuicao,
        }
      : undefined,
  );

  // Hook para suspender reserva
  const {
    loading: loadingSuspenderReserva,
    response: responseSuspenderReserva,
    fetchData: suspenderReserva,
    setResponse: setResponseSuspenderReserva,
  } = AtivacaoSuspensao.useSuspenderReserva(
    contribuicaoParaSuspender?.tipo === 'reserva'
      ? {
          NumeroBeneficio: contribuicaoParaSuspender.beneficioId,
          NumeroPlano: contribuicaoParaSuspender.planoId,
          TipoContribuicao: contribuicaoParaSuspender.tipoContribuicao,
        }
      : undefined,
  );

  const loadingSuspender = loadingSuspenderCuidado || loadingSuspenderReserva;

  const processarRespostaSuspensao = useCallback(
    (response: AtivacaoSuspensao.IResponseAcaoCoberturas, tipo: string) => {
      if (response.sucessoGI && response.sucessoBFF) {
        onSucessoSuspensao();
        onRecarregarDados();
      } else if (!response.sucessoBFF && response.mensagens?.length) {
        const mensagemErro =
          response.mensagens[0]?.descricao ||
          `Erro ao cancelar ${tipo}. Tente novamente.`;
        onErroSuspensao(mensagemErro);
      }
    },
    [onSucessoSuspensao, onErroSuspensao, onRecarregarDados],
  );

  useEffect(() => {
    if (responseSuspenderCuidado?.entidade) {
      processarRespostaSuspensao(
        responseSuspenderCuidado.entidade,
        'Cuidado Extra',
      );
    }
  }, [responseSuspenderCuidado, processarRespostaSuspensao]);

  useEffect(() => {
    if (responseSuspenderReserva?.entidade) {
      processarRespostaSuspensao(responseSuspenderReserva.entidade, 'Reserva');
    }
  }, [responseSuspenderReserva, processarRespostaSuspensao]);

  const confirmarSuspensao = useCallback(
    async (toggleContribuicao: (index: number) => void) => {
      if (!contribuicaoParaSuspender || !clienteAceita) return;

      try {
        if (contribuicaoParaSuspender.tipo === 'cuidadoExtra') {
          await suspenderCuidadoExtra();
        } else if (contribuicaoParaSuspender.tipo === 'reserva') {
          await suspenderReserva();
        }

        toggleContribuicao(contribuicaoParaSuspender.index);
      } catch (error) {
        const mensagemErro =
          contribuicaoParaSuspender.tipo === 'cuidadoExtra'
            ? 'Erro ao cancelar o Cuidado Extra. Tente novamente.'
            : 'Erro ao cancelar a Reserva. Tente novamente.';
        onErroSuspensao(mensagemErro);
      }
    },
    [
      contribuicaoParaSuspender,
      clienteAceita,
      suspenderCuidadoExtra,
      suspenderReserva,
      onErroSuspensao,
    ],
  );

  // Função para limpar respostas dos hooks
  const limparRespostas = useCallback(() => {
    setResponseSuspenderCuidado(undefined);
    setResponseSuspenderReserva(undefined);
  }, [setResponseSuspenderCuidado, setResponseSuspenderReserva]);

  return {
    loadingSuspender,
    confirmarSuspensao,
    limparRespostas,
  };
};
