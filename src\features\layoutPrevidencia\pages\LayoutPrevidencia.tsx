import * as LayoutPrevidenciaExports from '@src/features/layoutPrevidencia/exports';

const LayoutPrevidencia: React.FC = () => {
  const { initialized } = LayoutPrevidenciaExports.useRegistrarTokenOperador();
  const navigate = LayoutPrevidenciaExports.useNavigate();

  const { modalImprDocDispatch, modalImprDocState } =
    LayoutPrevidenciaExports.useContext(
      LayoutPrevidenciaExports.ModalImprimirDocumentosContext,
    );

  const { statusContratoFilter } = LayoutPrevidenciaExports.useContext(
    LayoutPrevidenciaExports.PrevidenciaContext,
  );

  return (
    <LayoutPrevidenciaExports.LayoutPlataforma
      primaryTitle={LayoutPrevidenciaExports.PREVIDENCIA_CONSTANTS.primaryTitle}
      buttonsRedirect={LayoutPrevidenciaExports.REDIRECT_BUTTONS()}
      secondaryTitle={
        LayoutPrevidenciaExports.PREVIDENCIA_CONSTANTS.secondaryTitle
      }
      userProfile="OPERADOR"
      profileFilters={statusContratoFilter}
      filterBykey="situacao"
    >
      <LayoutPrevidenciaExports.SwitchCase
        fallback={
          <LayoutPrevidenciaExports.Grid margin="50px" justify="center">
            <LayoutPrevidenciaExports.LoadingSpinner size="big">
              {LayoutPrevidenciaExports.LOADING_INSURANCE}
            </LayoutPrevidenciaExports.LoadingSpinner>{' '}
          </LayoutPrevidenciaExports.Grid>
        }
      >
        <LayoutPrevidenciaExports.Match when={initialized}>
          <LayoutPrevidenciaExports.ModalImprDoc
            modalImprDocDispatch={modalImprDocDispatch}
            modalImprDocState={modalImprDocState}
            navigate={navigate}
          />
          <LayoutPrevidenciaExports.ContainerListaCertificados />
        </LayoutPrevidenciaExports.Match>
      </LayoutPrevidenciaExports.SwitchCase>
    </LayoutPrevidenciaExports.LayoutPlataforma>
  );
};

export default LayoutPrevidencia;
