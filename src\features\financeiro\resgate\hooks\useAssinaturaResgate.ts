import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useAssinaturaResgate = (
  temContaPreenchida: boolean,
): Resgate.IUseAssinaturaResgate => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const { assinaturaValida, validarAssinatura } =
    Resgate.useValidarAssinatura();

  const { invocarApiGatewayCvpComToken: registrarTokenAssinaturaCaixa } =
    Resgate.useRegistrarTokenAssinaturaCaixa();

  const { confirmarAssinatura, loading: isLoadingConfirmarAssinatura } =
    Resgate.useConfirmarOperacaoAssinaturaCaixa();

  const obterAssinatura = (response: Resgate.IAssinaturaResponse): void => {
    Resgate.setSessionItem(Resgate.ASSINATURA_SESSION_KEY, response);
    registrarTokenAssinaturaCaixa(response);
    validarAssinatura(response);
  };

  const confirmarAssinaturaResgate = async (
    codigoSolicitacao?: string,
  ): Promise<boolean> => {
    const assinaturaResponse =
      Resgate.getSessionItem<Resgate.IAssinaturaResponse>(
        Resgate.ASSINATURA_SESSION_KEY,
      );

    const confirmacaoAssinatura = await confirmarAssinatura({
      tipoOperacao: Resgate.OPERACOES_PREVIDENCIA.RESGATE,
      metaDadoConfirmacao: Resgate.tryGetValueOrDefault(
        [assinaturaResponse?.resposta?.token],
        '',
      ),
      codigoSolicitacao: Resgate.tryGetValueOrDefault([codigoSolicitacao], ''),
    });

    return Resgate.tryGetValueOrDefault(
      [confirmacaoAssinatura?.entidade?.sucesso],
      false,
    );
  };

  const podeExibirAssinatura: boolean = Resgate.checkIfAllItemsAreTrue([
    !!formik.values.motivoResgate,
    temContaPreenchida,
  ]);

  return {
    obterAssinatura,
    confirmarAssinaturaResgate,
    isLoadingConfirmarAssinatura,
    podeExibirAssinatura,
    assinaturaValida,
  };
};
